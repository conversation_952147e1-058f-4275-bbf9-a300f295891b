<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Birthday implements ResultInterface
{
    use RawResponseTrait;

    private BirthdayNumber $birthdayNumber;

    public function __construct(BirthdayNumber $birthdayNumber)
    {
        $this->birthdayNumber = $birthdayNumber;
    }

    public function getBirthdayNumber(): BirthdayNumber
    {
        return $this->birthdayNumber;
    }
}
