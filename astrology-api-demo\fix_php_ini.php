<?php

$phpIniPath = 'C:\php\php.ini';

echo "Fixing PHP configuration...\n";

// Read the current php.ini content
$content = file_get_contents($phpIniPath);

// Enable extension_dir
$content = str_replace(';extension_dir = "ext"', 'extension_dir = "ext"', $content);

// Make sure cURL is enabled (it should already be)
if (strpos($content, 'extension=curl') === false) {
    // Add curl extension if not present
    $content = str_replace('[PHP]', "[PHP]\nextension=curl", $content);
}

// Write back to php.ini
file_put_contents($phpIniPath, $content);

echo "PHP configuration updated!\n";
echo "Please restart your web server or PHP process.\n";

// Test if changes worked
echo "\nTesting PHP modules after change:\n";
exec('php -m | findstr curl', $output);
if (empty($output)) {
    echo "❌ cURL still not available. You may need to restart your terminal/command prompt.\n";
} else {
    echo "✅ cURL is now available!\n";
}

?>
