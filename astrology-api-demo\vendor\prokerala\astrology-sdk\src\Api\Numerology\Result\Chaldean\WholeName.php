<?php



namespace <PERSON>kerala\Api\Numerology\Result\Chaldean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;
use <PERSON>kerala\Api\Numerology\Result\Pythagorean\NameChart;

class WholeName implements ResultInterface
{
    use RawResponseTrait;

    private WholeNameNumber $wholeNameNumber;

    private NameChart $nameChart;

    public function __construct(WholeNameNumber $wholeNameNumber, NameChart $nameChart)
    {
        $this->wholeNameNumber = $wholeNameNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getWholeNameNumber(): WholeNameNumber
    {
        return $this->wholeNameNumber;
    }
}
