<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class UniversalYear implements ResultInterface
{
    use RawResponseTrait;

    private UniversalYearNumber $universalYearNumber;

    public function __construct(UniversalYearNumber $universalYearNumber)
    {
        $this->universalYearNumber = $universalYearNumber;
    }

    public function getUniversalYearNumber(): UniversalYearNumber
    {
        return $this->universalYearNumber;
    }
}
