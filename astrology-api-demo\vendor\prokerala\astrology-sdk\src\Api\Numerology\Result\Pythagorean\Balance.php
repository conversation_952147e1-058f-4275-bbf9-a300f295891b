<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Balance implements ResultInterface
{
    use RawResponseTrait;

    private BalanceNumber $balanceNumber;

    private NameChart $nameChart;

    public function __construct(BalanceNumber $balanceNumber, NameChart $nameChart)
    {
        $this->balanceNumber = $balanceNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getBalanceNumber(): BalanceNumber
    {
        return $this->balanceNumber;
    }
}
