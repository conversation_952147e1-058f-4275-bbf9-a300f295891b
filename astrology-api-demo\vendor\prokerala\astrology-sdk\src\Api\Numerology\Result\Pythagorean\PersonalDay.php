<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class PersonalDay implements ResultInterface
{
    use RawResponseTrait;

    private PersonalDayNumber $personalDayNumber;

    public function __construct(PersonalDayNumber $personalDayNumber)
    {
        $this->personalDayNumber = $personalDayNumber;
    }

    public function getPersonalDayNumber(): PersonalDayNumber
    {
        return $this->personalDayNumber;
    }
}
