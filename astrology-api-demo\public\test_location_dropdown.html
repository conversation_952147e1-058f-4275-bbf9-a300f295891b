<!DOCTYPE html>
<html>
<head>
    <title>Location Dropdown Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 10px; width: 300px; border: 1px solid #ccc; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Location Dropdown Test</h1>
    
    <div class="debug">
        <h3>Debug Info:</h3>
        <div id="debug-info">Loading...</div>
    </div>
    
    <form>
        <div class="form-group">
            <label>Location (with Prokerala script):</label>
            <input type="text" name="location" class="prokerala-location-input" placeholder="Enter Location" autocomplete="off">
        </div>
        
        <div class="form-group">
            <label>Regular input (for comparison):</label>
            <input type="text" name="regular" placeholder="Regular input">
        </div>
        
        <div id="form-hidden-fields"></div>
    </form>

    <script>
        const CLIENT_ID = '51adad01-aed2-4489-8b19-a53d99f2ef15';
        
        // Debug function
        function updateDebug(message) {
            document.getElementById('debug-info').innerHTML += '<br>' + message;
        }
        
        updateDebug('Starting location script test...');
        
        (function () {
            function loadScript(cb) {
                updateDebug('Attempting to load location script...');
                var script = document.createElement('script');
                script.src = 'https://client-api.prokerala.com/static/js/location.min.js';
                script.onload = function() {
                    updateDebug('Location script loaded successfully!');
                    cb();
                };
                script.onerror = function() {
                    updateDebug('ERROR: Failed to load location script!');
                };
                script.async = 1;
                document.head.appendChild(script);
            }

            function createInput(name, value) {
                const input = document.createElement('input');
                input.name = name;
                input.type = 'hidden';
                return input;
            }
            
            function initWidget(input) {
                updateDebug('Initializing widget for input: ' + input.name);
                const form = input.form;
                const inputPrefix = input.dataset.location_input_prefix ? input.dataset.location_input_prefix : '';
                const coordinates = createInput(inputPrefix +'coordinates');
                const timezone = createInput(inputPrefix +'timezone');
                form.appendChild(coordinates);
                form.appendChild(timezone);
                
                try {
                    new LocationSearch(input, function (data) {
                        updateDebug('Location selected: ' + data.name + ' (' + data.latitude + ',' + data.longitude + ')');
                        coordinates.value = `${data.latitude},${data.longitude}`;
                        timezone.value = data.timezone;
                        input.setCustomValidity('');
                    }, {clientId: CLIENT_ID, persistKey: `${inputPrefix}loc`});
                    
                    updateDebug('LocationSearch widget initialized successfully!');
                } catch (e) {
                    updateDebug('ERROR initializing LocationSearch: ' + e.message);
                }

                input.addEventListener('change', function (e) {
                    input.setCustomValidity('Please select a location from the suggestions list');
                });
            }
            
            loadScript(function() {
                updateDebug('Script loaded, looking for location inputs...');
                let location = document.querySelectorAll('.prokerala-location-input');
                updateDebug('Found ' + location.length + ' location inputs');
                Array.from(location).map(initWidget);
            });
        })();
    </script>
</body>
</html>
