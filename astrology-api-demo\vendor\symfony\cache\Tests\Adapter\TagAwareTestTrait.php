<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) Fabien Potencier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Cache\Tests\Adapter;

use Symfony\Component\Cache\CacheItem;

/**
 * Common assertions for TagAware adapters.
 *
 * @method \Symfony\Component\Cache\Adapter\TagAwareAdapterInterface createCachePool(int $defaultLifetime = 0) Must be implemented by TestCase
 */
trait TagAwareTestTrait
{
    public function testInvalidTag()
    {
        $pool = $this->createCachePool();
        $item = $pool->getItem('foo');

        $this->expectException(\Psr\Cache\InvalidArgumentException::class);

        $item->tag(':');
    }

    public function testInvalidateTags()
    {
        $pool = $this->createCachePool();

        $i0 = $pool->getItem('i0');
        $i1 = $pool->getItem('i1');
        $i2 = $pool->getItem('i2');
        $i3 = $pool->getItem('i3');
        $foo = $pool->getItem('foo');

        $pool->save($i0->tag('bar'));
        $pool->save($i1->tag('foo'));
        $pool->save($i2->tag('foo')->tag('bar'));
        $pool->save($i3->tag('foo')->tag('baz'));
        $pool->save($foo);

        $pool->invalidateTags(['bar']);

        $this->assertFalse($pool->getItem('i0')->isHit());
        $this->assertTrue($pool->getItem('i1')->isHit());
        $this->assertFalse($pool->getItem('i2')->isHit());
        $this->assertTrue($pool->getItem('i3')->isHit());
        $this->assertTrue($pool->getItem('foo')->isHit());

        $pool->invalidateTags(['foo']);

        $this->assertFalse($pool->getItem('i1')->isHit());
        $this->assertFalse($pool->getItem('i3')->isHit());
        $this->assertTrue($pool->getItem('foo')->isHit());

        $anotherPoolInstance = $this->createCachePool();

        $this->assertFalse($anotherPoolInstance->getItem('i1')->isHit());
        $this->assertFalse($anotherPoolInstance->getItem('i3')->isHit());
        $this->assertTrue($anotherPoolInstance->getItem('foo')->isHit());
    }

    public function testInvalidateCommits()
    {
        $pool = $this->createCachePool();

        $foo = $pool->getItem('foo');
        $foo->tag('tag');

        $pool->saveDeferred($foo->set('foo'));
        $pool->invalidateTags(['tag']);

        // ??: This seems to contradict a bit logic in deleteItems, where it does unset($this->deferred[$key]); on key matches

        $foo = $pool->getItem('foo');

        $this->assertTrue($foo->isHit());
    }

    public function testTagsAreCleanedOnSave()
    {
        $pool = $this->createCachePool();

        $i = $pool->getItem('k');
        $pool->save($i->tag('foo'));

        $i = $pool->getItem('k');
        $pool->save($i->tag('bar'));

        $pool->invalidateTags(['foo']);
        $this->assertTrue($pool->getItem('k')->isHit());
    }

    public function testTagsAreCleanedOnDelete()
    {
        $pool = $this->createCachePool();

        $i = $pool->getItem('k');
        $pool->save($i->tag('foo'));
        $pool->deleteItem('k');

        $pool->save($pool->getItem('k'));
        $pool->invalidateTags(['foo']);

        $this->assertTrue($pool->getItem('k')->isHit());
    }

    public function testTagItemExpiry()
    {
        if (isset($this->skippedTests[__FUNCTION__])) {
            $this->markTestSkipped($this->skippedTests[__FUNCTION__]);
        }

        $pool = $this->createCachePool(10);

        $item = $pool->getItem('foo');
        $item->tag(['baz']);
        $item->expiresAfter(100);

        $pool->save($item);
        $pool->invalidateTags(['baz']);
        $this->assertFalse($pool->getItem('foo')->isHit());

        sleep(20);

        $this->assertFalse($pool->getItem('foo')->isHit());
    }

    public function testGetMetadata()
    {
        $pool = $this->createCachePool();

        $i = $pool->getItem('k');
        $this->assertSame([], $i->getMetadata());
        $pool->save($i->tag('foo'));
        $this->assertSame(['foo' => 'foo'], $i->getMetadata()[CacheItem::METADATA_TAGS]);

        $i = $pool->getItem('k');
        $this->assertSame(['foo' => 'foo'], $i->getMetadata()[CacheItem::METADATA_TAGS]);
    }

    public function testRefreshAfterExpires()
    {
        $pool = $this->createCachePool();
        $pool->clear();

        $cacheItem = $pool->getItem('test');

        $this->assertFalse($cacheItem->isHit());

        // write cache with expires
        $cacheItem->set('test');
        $cacheItem->tag('1234');
        $cacheItem->expiresAfter(1);

        $pool->save($cacheItem);

        $cacheItem = $pool->getItem('test');
        $this->assertTrue($cacheItem->isHit());

        // wait until expired
        sleep(2);

        // item should not longer be a hit
        $cacheItem = $pool->getItem('test');
        $this->assertFalse($cacheItem->isHit());

        // update expired item
        $cacheItem->set('test');
        $cacheItem->tag('1234');
        $cacheItem->expiresAfter(1);

        $pool->save($cacheItem);

        // item should be again a hit
        $cacheItem = $pool->getItem('test');
        $this->assertTrue($cacheItem->isHit());
    }
}
