<?php

// Simple test script to debug API connection
require __DIR__ . '/config.php';

echo "Testing API Connection...\n";
echo "Client ID: " . CLIENT_ID . "\n";
echo "Client Secret: " . substr(CLIENT_SECRET, 0, 10) . "...\n\n";

// Test 1: Direct cURL to token endpoint
echo "=== Test 1: Direct Token Request ===\n";

$data = [
    'grant_type' => 'client_credentials',
    'client_id' => CLIENT_ID,
    'client_secret' => CLIENT_SECRET,
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.prokerala.com/token');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: ProkeralaAPI-Test/1.0'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

echo "HTTP Code: " . $httpCode . "\n";
echo "cURL Error: " . ($curlError ?: 'None') . "\n";
echo "Response: " . $response . "\n\n";

curl_close($ch);

if ($httpCode === 200) {
    $tokenData = json_decode($response, true);
    if (isset($tokenData['access_token'])) {
        echo "✅ Token obtained successfully!\n";
        $token = $tokenData['access_token'];
        
        // Test 2: API call with token
        echo "\n=== Test 2: API Call with Token ===\n";
        
        $apiUrl = 'https://api.prokerala.com/v2/numerology/life-path-number?' . http_build_query([
            'datetime' => '2025-01-01T00:00:00+05:30'
        ]);
        
        $ch2 = curl_init();
        curl_setopt($ch2, CURLOPT_URL, $apiUrl);
        curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch2, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch2, CURLOPT_TIMEOUT, 30);
        
        $apiResponse = curl_exec($ch2);
        $apiHttpCode = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
        $apiCurlError = curl_error($ch2);
        
        echo "API HTTP Code: " . $apiHttpCode . "\n";
        echo "API cURL Error: " . ($apiCurlError ?: 'None') . "\n";
        echo "API Response: " . $apiResponse . "\n";
        
        curl_close($ch2);
        
        if ($apiHttpCode === 200) {
            echo "✅ API call successful!\n";
        } else {
            echo "❌ API call failed\n";
        }
    } else {
        echo "❌ No access token in response\n";
    }
} else {
    echo "❌ Failed to get token\n";
}

echo "\n=== Test 3: Network Connectivity ===\n";
$testUrls = [
    'https://google.com',
    'https://api.prokerala.com',
];

foreach ($testUrls as $url) {
    $ch3 = curl_init();
    curl_setopt($ch3, CURLOPT_URL, $url);
    curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch3, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch3, CURLOPT_NOBODY, true); // HEAD request only
    
    $result = curl_exec($ch3);
    $code = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
    $error = curl_error($ch3);
    
    echo $url . ": " . ($error ? "❌ " . $error : "✅ HTTP " . $code) . "\n";
    
    curl_close($ch3);
}

echo "\nTest completed.\n";
?>
