<!DOCTYPE html>
<html>
<head>
    <title>Location Search Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        input[type="text"] { width: 300px; padding: 8px; margin: 5px 0; }
        .results { margin-top: 10px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>Location Search Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Location Input</h2>
        <p class="info">Type a city name like "New York" or "London" to test autocomplete:</p>
        <input type="text" id="location-test" placeholder="Enter a city name..." autocomplete="off">
        <div id="location-results" class="results" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: API Connection Status</h2>
        <div id="api-status">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: JavaScript Console Logs</h2>
        <p class="info">Check browser console (F12) for any JavaScript errors</p>
        <button onclick="testConsole()">Test Console Output</button>
        <div id="console-output"></div>
    </div>

    <script>
        const CLIENT_ID = '51adad01-aed2-4489-8b19-a53d99f2ef15';
        
        // Test API connection
        function testApiConnection() {
            fetch('https://client-api.prokerala.com/static/js/location.min.js')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('api-status').innerHTML = 
                            '<span class="success">✅ Location API is accessible</span>';
                        loadLocationScript();
                    } else {
                        document.getElementById('api-status').innerHTML = 
                            '<span class="error">❌ Location API is not accessible</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('api-status').innerHTML = 
                        '<span class="error">❌ Error connecting to Location API: ' + error.message + '</span>';
                });
        }
        
        // Load location search script
        function loadLocationScript() {
            const script = document.createElement('script');
            script.src = 'https://client-api.prokerala.com/static/js/location.min.js';
            script.onload = function() {
                console.log('Location script loaded successfully');
                initLocationSearch();
            };
            script.onerror = function() {
                console.error('Failed to load location script');
                document.getElementById('api-status').innerHTML += 
                    '<br><span class="error">❌ Failed to load location script</span>';
            };
            document.head.appendChild(script);
        }
        
        // Initialize location search
        function initLocationSearch() {
            const input = document.getElementById('location-test');
            const resultsDiv = document.getElementById('location-results');
            
            try {
                // Create hidden inputs for coordinates and timezone
                const coordinatesInput = document.createElement('input');
                coordinatesInput.type = 'hidden';
                coordinatesInput.name = 'coordinates';
                
                const timezoneInput = document.createElement('input');
                timezoneInput.type = 'hidden';
                timezoneInput.name = 'timezone';
                
                document.body.appendChild(coordinatesInput);
                document.body.appendChild(timezoneInput);
                
                // Initialize LocationSearch
                new LocationSearch(input, function(data) {
                    console.log('Location selected:', data);
                    coordinatesInput.value = `${data.latitude},${data.longitude}`;
                    timezoneInput.value = data.timezone;
                    
                    resultsDiv.style.display = 'block';
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Location Selected Successfully!</div>
                        <strong>Place:</strong> ${data.name}<br>
                        <strong>Coordinates:</strong> ${data.latitude}, ${data.longitude}<br>
                        <strong>Timezone:</strong> ${data.timezone}
                    `;
                    
                    input.setCustomValidity('');
                }, {
                    clientId: CLIENT_ID,
                    persistKey: 'test-location'
                });
                
                // Add validation message on change
                input.addEventListener('change', function(e) {
                    if (!coordinatesInput.value) {
                        input.setCustomValidity('Please select a location from the suggestions list');
                        resultsDiv.style.display = 'block';
                        resultsDiv.innerHTML = '<div class="error">⚠️ Please select a location from the dropdown suggestions</div>';
                    }
                });
                
                console.log('Location search initialized successfully');
                document.getElementById('api-status').innerHTML += 
                    '<br><span class="success">✅ Location search initialized</span>';
                    
            } catch (error) {
                console.error('Error initializing location search:', error);
                document.getElementById('api-status').innerHTML += 
                    '<br><span class="error">❌ Error initializing location search: ' + error.message + '</span>';
            }
        }
        
        // Test console output
        function testConsole() {
            console.log('CLIENT_ID:', CLIENT_ID);
            console.log('LocationSearch available:', typeof LocationSearch !== 'undefined');
            
            document.getElementById('console-output').innerHTML = `
                <div class="info">Console test completed. Check browser console (F12) for details.</div>
                <div>CLIENT_ID: ${CLIENT_ID}</div>
                <div>LocationSearch available: ${typeof LocationSearch !== 'undefined'}</div>
            `;
        }
        
        // Start tests when page loads
        window.onload = function() {
            testApiConnection();
        };
    </script>
</body>
</html>
