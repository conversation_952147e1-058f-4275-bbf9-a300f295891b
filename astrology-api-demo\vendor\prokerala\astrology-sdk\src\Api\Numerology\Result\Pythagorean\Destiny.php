<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Destiny implements ResultInterface
{
    use RawResponseTrait;

    private DestinyNumber $destinyNumber;

    private NameChart $nameChart;

    public function __construct(DestinyNumber $destinyNumber, NameChart $nameChart)
    {
        $this->destinyNumber = $destinyNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getDestinyNumber(): DestinyNumber
    {
        return $this->destinyNumber;
    }
}
