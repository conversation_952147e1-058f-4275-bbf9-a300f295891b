<?xml version="1.0" encoding="UTF-8"?>
  <phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
           bootstrap="./tests/bootstrap.php"
           backupGlobals="true"
           colors="true"
           executionOrder="random"
           failOnRisky="true"
           failOnWarning="true"
           convertDeprecationsToExceptions="true"
  >
  <testsuites>
    <testsuite name="Test Suite">
      <directory>tests</directory>
    </testsuite>
  </testsuites>

  <php>
    <server name="TEST_SERVER" value="http://127.0.0.1:10000/server.php" />
  </php>

  <filter>
    <whitelist>
      <directory suffix=".php">src</directory>
      <exclude>
        <directory suffix="Interface.php">src/</directory>
      </exclude>
    </whitelist>
  </filter>
</phpunit>
