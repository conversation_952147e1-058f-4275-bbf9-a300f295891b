<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Cache\Adapter;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\Cache\CacheItem;

// Help opcache.preload discover always-needed symbols
class_exists(CacheItem::class);

/**
 * Interface for adapters managing instances of Symfony's CacheItem.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface AdapterInterface extends CacheItemPoolInterface
{
    public function getItem(mixed $key): CacheItem;

    /**
     * @return iterable<string, CacheItem>
     */
    public function getItems(array $keys = []): iterable;

    public function clear(string $prefix = ''): bool;
}
