<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Bridge implements ResultInterface
{
    use RawResponseTrait;

    private BridgeNumber $bridgeNumber;

    public function __construct(BridgeNumber $bridgeNumber)
    {
        $this->bridgeNumber = $bridgeNumber;
    }

    public function getBridgeNumber(): BridgeNumber
    {
        return $this->bridgeNumber;
    }
}
