<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON>cier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Cache\Adapter;

use Psr\Cache\CacheItemInterface;
use Symfony\Component\Cache\CacheItem;
use Symfony\Contracts\Cache\CacheInterface;

/**
 * <AUTHOR> <<EMAIL>>
 */
class NullAdapter implements AdapterInterface, CacheInterface
{
    private static \Closure $createCacheItem;

    public function __construct()
    {
        self::$createCacheItem ??= \Closure::bind(
            static function ($key) {
                $item = new CacheItem();
                $item->key = $key;
                $item->isHit = false;

                return $item;
            },
            null,
            CacheItem::class
        );
    }

    public function get(string $key, callable $callback, ?float $beta = null, ?array &$metadata = null): mixed
    {
        $save = true;

        return $callback((self::$createCacheItem)($key), $save);
    }

    public function getItem(mixed $key): CacheItem
    {
        return (self::$createCacheItem)($key);
    }

    public function getItems(array $keys = []): iterable
    {
        return $this->generateItems($keys);
    }

    public function hasItem(mixed $key): bool
    {
        return false;
    }

    public function clear(string $prefix = ''): bool
    {
        return true;
    }

    public function deleteItem(mixed $key): bool
    {
        return true;
    }

    public function deleteItems(array $keys): bool
    {
        return true;
    }

    public function save(CacheItemInterface $item): bool
    {
        return true;
    }

    public function saveDeferred(CacheItemInterface $item): bool
    {
        return true;
    }

    public function commit(): bool
    {
        return true;
    }

    public function delete(string $key): bool
    {
        return $this->deleteItem($key);
    }

    private function generateItems(array $keys): \Generator
    {
        $f = self::$createCacheItem;

        foreach ($keys as $key) {
            yield $key => $f($key);
        }
    }
}
