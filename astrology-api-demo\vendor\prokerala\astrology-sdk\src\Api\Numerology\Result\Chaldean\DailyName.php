<?php



namespace <PERSON>kerala\Api\Numerology\Result\Chaldean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;
use <PERSON>kerala\Api\Numerology\Result\Pythagorean\NameChart;

class DailyName implements ResultInterface
{
    use RawResponseTrait;

    private DailyNameNumber $dailyNameNumber;

    private NameChart $nameChart;

    public function __construct(DailyNameNumber $dailyNameNumber, NameChart $nameChart)
    {
        $this->dailyNameNumber = $dailyNameNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getDailyNameNumber(): DailyNameNumber
    {
        return $this->dailyNameNumber;
    }
}
