<?php



namespace <PERSON>kerala\Api\Numerology\Result\Chaldean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class LifePath implements ResultInterface
{
    use RawResponseTrait;

    private LifePathNumber $lifePathNumber;

    public function __construct(LifePathNumber $lifePathNumber)
    {
        $this->lifePathNumber = $lifePathNumber;
    }

    public function getLifePathNumber(): LifePathNumber
    {
        return $this->lifePathNumber;
    }
}
