<?php

echo "=== Fixing All Coordinates Explode Issues ===\n\n";

$files = [
    'anandadi-yoga.php', 'ashtakavarga.php', 'auspicious-period.php', 'auspicious-yoga.php',
    'chandra-bala.php', 'chandrashtama-periods.php', 'chart.php', 'choghadiya.php',
    'dasha-periods.php', 'disha-shool.php', 'gowri-nalla-neram.php', 'hindu-panchang.php',
    'hora.php', 'inauspicious-period.php', 'kaal-sarp-dosha.php', 'kundli.php',
    'malayalam-panchang.php', 'mangal-dosha.php', 'natal-chart.php', 'papasamyam.php',
    'planet-position.php', 'planet-relationship.php', 'progression-chart.php', 'ritu.php',
    'sade-sati.php', 'solar-return-chart.php', 'solstice.php', 'sudarshana-chakra.php',
    'tamil-panchang.php', 'tara-bala.php', 'telugu-panchang.php', 'transit-chart.php',
    'yoga-details.php'
];

$fixedCount = 0;

foreach ($files as $filename) {
    $filepath = __DIR__ . '/public/' . $filename;
    
    if (!file_exists($filepath)) {
        echo "⚠️  File not found: $filename\n";
        continue;
    }
    
    $content = file_get_contents($filepath);
    $originalContent = $content;
    
    // Pattern 1: Fix basic coordinates assignment without null coalescing
    $content = preg_replace(
        '/(\$coordinates\s*=\s*\$_POST\[\'coordinates\'\];)/',
        '$coordinates = $_POST[\'coordinates\'] ?? \'\';',
        $content
    );
    
    // Pattern 2: Add null check before explode
    $content = preg_replace(
        '/(\s+)(\$arCoordinates\s*=\s*explode\(\',\',\s*\$coordinates\);\s*\$input\[\'latitude\'\]\s*=\s*\$arCoordinates\[0\]\s*\?\?\s*\'\';\s*\$input\[\'longitude\'\]\s*=\s*\$arCoordinates\[1\]\s*\?\?\s*\'\';)/s',
        '$1if (!empty($coordinates)) {' . "\n" . '$1    $arCoordinates = explode(\',\', $coordinates);' . "\n" . '$1    $input[\'latitude\'] = $arCoordinates[0] ?? \'\';' . "\n" . '$1    $input[\'longitude\'] = $arCoordinates[1] ?? \'\';' . "\n" . '$1}',
        $content
    );
    
    // If the above didn't work, try a simpler approach
    if ($content === $originalContent) {
        $lines = explode("\n", $content);
        $newLines = [];
        
        for ($i = 0; $i < count($lines); $i++) {
            $line = $lines[$i];
            
            // Fix coordinates assignment
            if (preg_match('/(\s*)\$coordinates\s*=\s*\$_POST\[\'coordinates\'\];/', $line, $matches)) {
                $newLines[] = $matches[1] . '$coordinates = $_POST[\'coordinates\'] ?? \'\';';
                continue;
            }
            
            // Fix explode with null check
            if (preg_match('/(\s*)\$arCoordinates\s*=\s*explode\(\',\',\s*\$coordinates\);/', $line, $matches)) {
                $indent = $matches[1];
                $newLines[] = $indent . 'if (!empty($coordinates)) {';
                $newLines[] = $indent . '    $arCoordinates = explode(\',\', $coordinates);';
                
                // Look for the next two lines with latitude/longitude
                $j = $i + 1;
                while ($j < count($lines) && $j < $i + 3) {
                    if (preg_match('/(\s*)\$input\[\'latitude\'\]/', $lines[$j])) {
                        $newLines[] = $indent . '    ' . trim($lines[$j]);
                        $i = $j;
                    } elseif (preg_match('/(\s*)\$input\[\'longitude\'\]/', $lines[$j])) {
                        $newLines[] = $indent . '    ' . trim($lines[$j]);
                        $i = $j;
                        break;
                    }
                    $j++;
                }
                
                $newLines[] = $indent . '}';
                continue;
            }
            
            $newLines[] = $line;
        }
        
        $content = implode("\n", $newLines);
    }
    
    if ($content !== $originalContent) {
        file_put_contents($filepath, $content);
        echo "✅ Fixed: $filename\n";
        $fixedCount++;
    } else {
        echo "⚠️  No changes needed: $filename\n";
    }
}

echo "\n=== Summary ===\n";
echo "Files processed: " . count($files) . "\n";
echo "Files fixed: $fixedCount\n";

// Special handling for files with multiple coordinates (kundli-matching, etc.)
$specialFiles = [
    'kundli-matching.php' => ['girl_coordinates', 'boy_coordinates'],
    'papasamyam-check.php' => ['girl_coordinates', 'boy_coordinates'],
    'porutham.php' => ['girl_coordinates', 'boy_coordinates']
];

echo "\n=== Fixing Special Files ===\n";

foreach ($specialFiles as $filename => $coordFields) {
    $filepath = __DIR__ . '/public/' . $filename;
    
    if (!file_exists($filepath)) {
        echo "⚠️  File not found: $filename\n";
        continue;
    }
    
    $content = file_get_contents($filepath);
    $originalContent = $content;
    
    foreach ($coordFields as $field) {
        // Fix assignment
        $content = preg_replace(
            '/(\$' . $field . '\s*=\s*\$_POST\[\'' . $field . '\'\];)/',
            '$' . $field . ' = $_POST[\'' . $field . '\'] ?? \'\';',
            $content
        );
        
        // Fix explode
        $content = preg_replace(
            '/(\s+)(\$' . $field . '_data\s*=\s*explode\(\',\',\s*\$' . $field . '\);)/',
            '$1if (!empty($' . $field . ')) {' . "\n" . '$1    $2' . "\n" . '$1}',
            $content
        );
    }
    
    if ($content !== $originalContent) {
        file_put_contents($filepath, $content);
        echo "✅ Fixed special file: $filename\n";
    } else {
        echo "⚠️  No changes needed for special file: $filename\n";
    }
}

echo "\n=== All Coordinates Issues Fixed! ===\n";

?>
