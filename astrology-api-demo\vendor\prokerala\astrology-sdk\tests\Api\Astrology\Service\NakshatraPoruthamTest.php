<?php

/*
 * This file is part of Prokerala Astrology API PHP SDK
 *
 * © Ennexa Technologies <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace <PERSON>kerala\Test\Api\Astrology\Service;

use <PERSON>kerala\Api\Astrology\NakshatraProfile;
use Prokerala\Api\Astrology\Result\HoroscopeMatching\AdvancedNakshatraPorutham as AdvancedPorutham;
use Prokerala\Api\Astrology\Result\HoroscopeMatching\NakshatraPorutham as BasicPorutham;
use Prokerala\Api\Astrology\Service\NakshatraPorutham;
use Prokerala\Test\Api\Common\Traits\AuthenticationTrait;
use Prokerala\Test\BaseTestCase;

/**
 * @internal
 * @coversNothing
 */
final class NakshatraPoruthamTest extends BaseTestCase
{
    use AuthenticationTrait;

    /**
     * @covers \Prokerala\Api\Astrology\Service\NakshatraPorutham::process
     */
    public function testProcess(): void
    {
        $service = new NakshatraPorutham($this->getClient());

        $la = 'en';
        $girlProfile = new NakshatraProfile(1, 2);
        $boyProfile = new NakshatraProfile(3, 4);

        $result = $service->process($girlProfile, $boyProfile, false, $la);
        $this->assertInstanceOf(BasicPorutham::class, $result);

        $result = $service->process($girlProfile, $boyProfile, true, $la);
        $this->assertInstanceOf(AdvancedPorutham::class, $result);
    }
}
