<?php

echo "=== Analyzing Hardcoded Values in Astrology API Demo ===\n\n";

$publicDir = __DIR__ . '/public';
$templatesDir = __DIR__ . '/templates';

$issues = [];

// Function to scan PHP files for hardcoded values
function scanForHardcodedValues($file, $content) {
    $hardcodedPatterns = [
        'datetime' => '/[\'"](\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2})[\'"]/',
        'coordinates' => '/[\'"](-?\d+\.?\d*,-?\d+\.?\d*)[\'"]/',
        'place' => '/[\'"]([A-Za-z\s,]+(?:USA|UK|India|London|New York|Delhi|Mumbai))[\'"]/',
        'disabled_inputs' => '/disabled[^>]*>/',
        'readonly_inputs' => '/readonly[^>]*>/',
    ];
    
    $found = [];
    
    foreach ($hardcodedPatterns as $type => $pattern) {
        if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
            foreach ($matches[1] as $match) {
                $lineNumber = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                $found[] = [
                    'type' => $type,
                    'value' => $match[0],
                    'line' => $lineNumber
                ];
            }
        }
    }
    
    return $found;
}

// Scan public PHP files
$phpFiles = glob($publicDir . '/*.php');
foreach ($phpFiles as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    
    $hardcoded = scanForHardcodedValues($file, $content);
    if (!empty($hardcoded)) {
        $issues[$filename] = $hardcoded;
    }
}

// Scan template files
$templateFiles = glob($templatesDir . '/**/*.php', GLOB_BRACE);
foreach ($templateFiles as $file) {
    $filename = str_replace($templatesDir . '/', '', $file);
    $content = file_get_contents($file);
    
    $hardcoded = scanForHardcodedValues($file, $content);
    if (!empty($hardcoded)) {
        $issues['templates/' . $filename] = $hardcoded;
    }
}

// Display results
echo "Found hardcoded values in the following files:\n";
echo str_repeat("=", 60) . "\n";

foreach ($issues as $file => $problems) {
    echo "\n📁 FILE: $file\n";
    echo str_repeat("-", 40) . "\n";
    
    foreach ($problems as $problem) {
        $icon = match($problem['type']) {
            'datetime' => '📅',
            'coordinates' => '🌍',
            'place' => '📍',
            'disabled_inputs' => '🚫',
            'readonly_inputs' => '🔒',
            default => '⚠️'
        };
        
        echo sprintf("%s Line %d: %s = %s\n", 
            $icon, 
            $problem['line'], 
            strtoupper($problem['type']), 
            $problem['value']
        );
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "SUMMARY:\n";
echo "- Total files with issues: " . count($issues) . "\n";

$totalIssues = array_sum(array_map('count', $issues));
echo "- Total hardcoded values found: $totalIssues\n";

// Categorize issues
$categories = [];
foreach ($issues as $file => $problems) {
    foreach ($problems as $problem) {
        $categories[$problem['type']][] = $file;
    }
}

echo "\nISSUES BY CATEGORY:\n";
foreach ($categories as $type => $files) {
    echo "- " . strtoupper($type) . ": " . count($files) . " occurrences\n";
}

echo "\n=== Analysis Complete ===\n";

?>
