<?php

declare(strict_types=1);

use Prokerala\Api\Astrology\Result\Planet;
use Prokerala\Api\Report\Service\CompatibilityReport;
use Prokerala\Api\Report\Service\PersonalReport;
use Prokerala\Common\Api\Exception\AuthenticationException;
use Prokerala\Common\Api\Exception\Exception;
use Prokerala\Common\Api\Exception\QuotaExceededException;
use Prokerala\Common\Api\Exception\RateLimitExceededException;
use Prokerala\Common\Api\Exception\ValidationException;

require __DIR__ . '/bootstrap.php';

$submit = $_POST['submit'] ?? 0;
$sample_name = 'pdf-report';
$report_mode = isset($_POST['report_mode']) ? $_POST['report_mode'] : (isset($_GET['report_mode']) ? $_GET['report_mode'] : 'personal-report');

if (!in_array($report_mode, ['personal-report', 'compatibility-report'], true)) {
    $report_mode = 'personal-report';
}

$timezone = 'Asia/Kolkata';
$chartType = 'north-indian';
$planet = Planet::SUN;
$planetAshtakaVarga = 'all';
if (isset($_POST['submit'])) {
    if ('personal-report' === $report_mode) {
        $firstName = $_POST['first_name'];
        $middleName = $_POST['middle_name'];
        $lastName = $_POST['last_name'];
        $gender = $_POST['gender'];
        $chartType = $_POST['chart_type'];
        $planet = $_POST['planet'];
        $planetAshtakaVarga = $_POST['planet_ashtakavarga'];
        $datetime = $_POST['datetime'];
        $coordinates = $_POST['coordinates'];
        $place = $_POST['place'];
    } else {
        $girlFirstName = $_POST['girl_first_name'];
        $girlMiddleName = $_POST['girl_middle_name'];
        $girlLastName = $_POST['girl_last_name'];
        $girlDatetime = $_POST['girl_datetime'];
        $girlCoordinates = $_POST['girl_coordinates'];
        $girlPlace = $_POST['girl_place'];
        $boyFirstName = $_POST['boy_first_name'];
        $boyMiddleName = $_POST['boy_middle_name'];
        $boyLastName = $_POST['boy_last_name'];
        $boyDatetime = $_POST['boy_datetime'];
        $boyCoordinates = $_POST['boy_coordinates'];
        $boyPlace = $_POST['boy_place'];
    }
    $reportName = $_POST['report_name'] ?: 'Sample Report';
    $report = $_POST['report'];
    $reportCaption = $_POST['report_caption'];
    $brandName = $_POST['brand_name'] ?: 'Prokerala';
    $footer = $_POST['footer'] ?: '<a href="https://www.prokerala.com">prokerala.com</a> | 📧 <EMAIL> | Call Now: 1800 425 0053';
}

$result = [];
$errors = [];

$reportTypes = [
    'personal-report' => [
        'mangal-dosha-report' => [
            ['name' => 'birth-details'],
            ['name' => 'chart', 'options' => ['chart_style' => $chartType]],
            ['name' => 'planet-position'],
            ['name' => 'mangal-dosha', 'options' => ['chart_style' => $chartType]],
        ],
        'personal-report' => [
            ['name' => 'birth-details'],
            ['name' => 'chart', 'options' => ['chart_style' => $chartType]],
            ['name' => 'planet-position'],
            ['name' => 'sudharshanachakra-chart'],
            ['name' => 'mangal-dosha', 'options' => ['chart_style' => $chartType]],
            ['name' => 'yoga-details'],
            ['name' => 'kaal-sarp-dosha', 'options' => ['chart_style' => $chartType]],
            ['name' => 'planet-relationship'],
            ['name' => 'sarvashtakavarga-chart', 'options' => ['chart_style' => $chartType, 'planet_ashtakavarga' => $planetAshtakaVarga]],
            ['name' => 'sade-sati', 'options' => ['chart_style' => $chartType]],
            ['name' => 'shodashvarga-chart', 'options' => ['chart_style' => 'south-indian']],
            ['name' => 'dasa-periods'],
            ['name' => 'papa-dosha', 'options' => ['chart_style' => $chartType]],
        ],
    ],
    'compatibility-report' => [
        'kundli-matching' => [
            ['name' => 'birth-details', 'options' => ['chart_style' => 'north-indian']],
            ['name' => 'mangal-dosha'],
            ['name' => 'kundli-matching'],
        ],
        'tamil-porutham' => [
            ['name' => 'birth-details', 'options' => ['chart_style' => 'south-indian']],
            ['name' => 'mangal-dosha'],
            ['name' => 'porutham-tamil'],
        ],
        'kerala-porutham' => [
            ['name' => 'birth-details', 'options' => ['chart_style' => 'south-indian']],
            ['name' => 'mangal-dosha'],
            ['name' => 'porutham-kerala'],
        ],
    ],
];

if ($submit) {
    try {
        if ('personal-report' === $report_mode) {
            $method = new PersonalReport($client);
            $input = [
                'first_name' => $firstName,
                'middle_name' => $middleName,
                'last_name' => $lastName,
                'datetime' => $datetime,
                'coordinates' => $coordinates,
                'place' => $place,
                'gender' => $gender,
            ];
        } else {
            $method = new CompatibilityReport($client);
            $input = [
                'first_person' => [
                    'first_name' => $girlFirstName,
                    'middle_name' => $girlMiddleName,
                    'last_name' => $girlLastName,
                    'datetime' => $girlDatetime,
                    'coordinates' => $girlCoordinates,
                    'place' => $girlPlace,
                    'gender' => 'female',
                ],
                'second_person' => [
                    'first_name' => $boyFirstName,
                    'middle_name' => $boyMiddleName,
                    'last_name' => $boyLastName,
                    'datetime' => $boyDatetime,
                    'coordinates' => $boyCoordinates,
                    'place' => $boyPlace,
                    'gender' => 'male',
                ],
            ];
        }

        $options = [
            'modules' => $reportTypes[$report_mode][$report],
            'template' => [
                'style' => 'basic',
                'footer' => $footer,
            ],
            'report' => [
                'name' => $reportName,
                'caption' => $reportCaption,
                'brand_name' => $brandName,
            ],
        ];

        $result = $method->process($input, $options);
        header('Content-Type: application/pdf');
        header('Content-disposition: attachment; filename="Prokerala Astrology Report.pdf"');

        echo $result;

        exit;
    } catch (ValidationException $e) {
        $errors = $e->getValidationErrors();
    } catch (QuotaExceededException $e) {
        $errors['message'] = 'ERROR: You have exceeded your quota allocation for the day';
    } catch (RateLimitExceededException $e) {
        $errors['message'] = 'ERROR: Rate limit exceeded. Throttle your requests.';
    } catch (AuthenticationException $e) {
        $errors = ['message' => $e->getMessage()];
    } catch (Exception $e) {
        $errors = ['message' => "API Request Failed with error {$e->getMessage()}"];
    }
}

$apiCreditUsed = $client->getCreditUsed();

include DEMO_BASE_DIR . '/templates/pdf-report.tpl.php';
