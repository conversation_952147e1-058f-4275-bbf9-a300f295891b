<?xml version="1.0" encoding="UTF-8"?>

<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/9.3/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         bootstrap="vendor/autoload.php"
         failOnRisky="true"
         failOnWarning="true"
>
    <php>
        <ini name="error_reporting" value="-1" />
        <env name="REDIS_HOST" value="localhost" />
        <env name="REDIS_SOCKET" value="/var/run/redis/redis-server.sock" />
        <env name="MEMCACHED_HOST" value="localhost" />
        <env name="COUCHBASE_HOST" value="localhost" />
        <env name="COUCHBASE_USER" value="Administrator" />
        <env name="COUCHBASE_PASS" value="111111" />
    </php>

    <testsuites>
        <testsuite name="Symfony Cache Component Test Suite">
            <directory>./Tests/</directory>
        </testsuite>
    </testsuites>

    <coverage>
        <include>
            <directory>./</directory>
        </include>
        <exclude>
            <directory>./Tests</directory>
            <directory>./vendor</directory>
        </exclude>
    </coverage>

    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener">
            <arguments>
                <array>
                    <element key="time-sensitive">
                        <array>
                            <element key="0"><string>Cache\IntegrationTests</string></element>
                            <element key="1"><string>Symfony\Component\Cache</string></element>
                            <element key="2"><string>Symfony\Component\Cache\Tests\Fixtures</string></element>
                            <element key="3"><string>Symfony\Component\Cache\Tests\Traits</string></element>
                            <element key="4"><string>Symfony\Component\Cache\Traits</string></element>
                        </array>
                    </element>
                </array>
            </arguments>
        </listener>
    </listeners>
</phpunit>
