{"name": "prokerala/astrology-sdk", "description": "Prokerala.com Astrology API Client Library for PHP.", "type": "library", "require": {"php": ">=8.0", "ext-json": "*", "psr/http-client": "^1.0", "psr/http-message": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-client-implementation": "^1.0"}, "license": "MIT", "config": {"sort-packages": true}, "authors": [{"name": "Prokerala", "email": "<EMAIL>", "homepage": "https://www.prokerala.com"}], "minimum-stability": "stable", "autoload": {"psr-4": {"Prokerala\\Api\\": "src/Api", "Prokerala\\Common\\Api\\": "src/Common/Api"}, "files": ["src/aliases.php"], "classmap": ["src/aliases.php"]}, "autoload-dev": {"psr-4": {"Prokerala\\Test\\": "tests"}}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation", "guzzlehttp/guzzle": "PSR-7 compatible Guzzle client", "symfony/cache": "PSR-16 Simple cache implementation"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.13", "guzzlehttp/guzzle": "^7", "nyholm/psr7": "^1.0", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "symfony/cache": "~3.4|~4.0|~5.0", "symfony/dotenv": "^6.1"}}