<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Pinnacle implements ResultInterface
{
    use RawResponseTrait;

    private PinnacleNumber $pinnacleNumber;

    public function __construct(PinnacleNumber $pinnacleNumber)
    {
        $this->pinnacleNumber = $pinnacleNumber;
    }

    public function getPinnacleNumber(): PinnacleNumber
    {
        return $this->pinnacleNumber;
    }
}
