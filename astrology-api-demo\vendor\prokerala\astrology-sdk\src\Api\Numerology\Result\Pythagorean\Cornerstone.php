<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Cornerstone implements ResultInterface
{
    use RawResponseTrait;

    private CornerstoneNumber $cornerstoneNumber;

    private NameChart $nameChart;

    public function __construct(CornerstoneNumber $cornerstoneNumber, NameChart $nameChart)
    {
        $this->cornerstoneNumber = $cornerstoneNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getCornerstoneNumber(): CornerstoneNumber
    {
        return $this->cornerstoneNumber;
    }
}
