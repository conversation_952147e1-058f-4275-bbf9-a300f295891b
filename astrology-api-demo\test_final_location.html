<!DOCTYPE html>
<html>
<head>
    <title>Final Location Search Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        input[type="text"] { width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .results { margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .test-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .demo-link { display: inline-block; margin: 10px; padding: 10px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; }
        .demo-link:hover { background: #1e7e34; color: white; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Location Search Functionality Test</h1>
        <p class="info">This page tests the location search functionality that was fixed across all astrology services.</p>
        
        <div class="test-section">
            <h2>📊 System Status</h2>
            <div id="system-status">
                <div class="test-item">
                    <span class="status-indicator status-warning"></span>
                    <span>Checking system status...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔍 Location Search Test</h2>
            <p class="info">Type a city name to test the autocomplete functionality:</p>
            <input type="text" id="location-test" placeholder="Try typing: New York, London, Mumbai, Delhi..." autocomplete="off">
            <div id="location-results" class="results" style="display:none;"></div>
            
            <h3>Test Suggestions:</h3>
            <button onclick="testLocation('New York')">Test: New York</button>
            <button onclick="testLocation('London')">Test: London</button>
            <button onclick="testLocation('Mumbai')">Test: Mumbai</button>
            <button onclick="testLocation('Delhi')">Test: Delhi</button>
            <button onclick="testLocation('Tokyo')">Test: Tokyo</button>
        </div>
        
        <div class="test-section">
            <h2>🚀 Live Demo Links</h2>
            <p class="info">Test the location search on actual astrology service pages:</p>
            <a href="/birth-details.php" class="demo-link" target="_blank">Birth Details</a>
            <a href="/kundli.php" class="demo-link" target="_blank">Kundli</a>
            <a href="/panchang.php" class="demo-link" target="_blank">Panchang</a>
            <a href="/planet-position.php" class="demo-link" target="_blank">Planet Position</a>
            <a href="/mangal-dosha.php" class="demo-link" target="_blank">Mangal Dosha</a>
        </div>
        
        <div class="test-section">
            <h2>📋 Test Results Summary</h2>
            <div id="test-summary">
                <div class="test-item">
                    <span class="status-indicator status-warning"></span>
                    <span>Running tests...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const CLIENT_ID = '51adad01-aed2-4489-8b19-a53d99f2ef15';
        let testResults = {
            apiConnection: false,
            scriptLoaded: false,
            locationSearchInitialized: false,
            locationSelected: false
        };
        
        // Test API connection
        function testApiConnection() {
            return fetch('https://client-api.prokerala.com/static/js/location.min.js')
                .then(response => {
                    testResults.apiConnection = response.ok;
                    return response.ok;
                })
                .catch(() => {
                    testResults.apiConnection = false;
                    return false;
                });
        }
        
        // Load location search script
        function loadLocationScript() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://client-api.prokerala.com/static/js/location.min.js';
                script.onload = () => {
                    testResults.scriptLoaded = true;
                    resolve(true);
                };
                script.onerror = () => {
                    testResults.scriptLoaded = false;
                    reject(false);
                };
                document.head.appendChild(script);
            });
        }
        
        // Initialize location search
        function initLocationSearch() {
            const input = document.getElementById('location-test');
            const resultsDiv = document.getElementById('location-results');
            
            try {
                // Create hidden inputs for coordinates and timezone
                const coordinatesInput = document.createElement('input');
                coordinatesInput.type = 'hidden';
                coordinatesInput.name = 'coordinates';
                coordinatesInput.id = 'test-coordinates';
                
                const timezoneInput = document.createElement('input');
                timezoneInput.type = 'hidden';
                timezoneInput.name = 'timezone';
                timezoneInput.id = 'test-timezone';
                
                document.body.appendChild(coordinatesInput);
                document.body.appendChild(timezoneInput);
                
                // Initialize LocationSearch
                new LocationSearch(input, function(data) {
                    testResults.locationSelected = true;
                    coordinatesInput.value = `${data.latitude},${data.longitude}`;
                    timezoneInput.value = data.timezone;
                    
                    resultsDiv.style.display = 'block';
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Location Selected Successfully!</div>
                        <strong>Place:</strong> ${data.name}<br>
                        <strong>Coordinates:</strong> ${data.latitude}, ${data.longitude}<br>
                        <strong>Timezone:</strong> ${data.timezone}<br>
                        <strong>Country:</strong> ${data.country || 'N/A'}<br>
                        <strong>State:</strong> ${data.state || 'N/A'}
                    `;
                    
                    input.setCustomValidity('');
                    updateTestSummary();
                }, {
                    clientId: CLIENT_ID,
                    persistKey: 'final-test-location'
                });
                
                // Add validation message on change
                input.addEventListener('change', function(e) {
                    if (!coordinatesInput.value) {
                        input.setCustomValidity('Please select a location from the suggestions list');
                        resultsDiv.style.display = 'block';
                        resultsDiv.innerHTML = '<div class="warning">⚠️ Please select a location from the dropdown suggestions</div>';
                    }
                });
                
                testResults.locationSearchInitialized = true;
                return true;
                
            } catch (error) {
                console.error('Error initializing location search:', error);
                testResults.locationSearchInitialized = false;
                return false;
            }
        }
        
        // Test specific location
        function testLocation(cityName) {
            const input = document.getElementById('location-test');
            input.value = cityName;
            input.focus();
            
            // Trigger input event to show suggestions
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);
        }
        
        // Update system status
        function updateSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            const items = [
                {
                    name: 'API Connection',
                    status: testResults.apiConnection,
                    description: 'Connection to Prokerala Location API'
                },
                {
                    name: 'Script Loading',
                    status: testResults.scriptLoaded,
                    description: 'Location search JavaScript library'
                },
                {
                    name: 'Location Search',
                    status: testResults.locationSearchInitialized,
                    description: 'Location search widget initialization'
                },
                {
                    name: 'CLIENT_ID',
                    status: CLIENT_ID && CLIENT_ID !== 'YOUR_CLIENT_ID',
                    description: 'API client credentials'
                }
            ];
            
            statusDiv.innerHTML = items.map(item => `
                <div class="test-item">
                    <span class="status-indicator ${item.status ? 'status-success' : 'status-error'}"></span>
                    <strong>${item.name}:</strong> ${item.status ? 'OK' : 'FAILED'} - ${item.description}
                </div>
            `).join('');
        }
        
        // Update test summary
        function updateTestSummary() {
            const summaryDiv = document.getElementById('test-summary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(Boolean).length;
            
            const summary = [
                `Total Tests: ${totalTests}`,
                `Passed: ${passedTests}`,
                `Failed: ${totalTests - passedTests}`,
                `Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`
            ];
            
            const statusClass = passedTests === totalTests ? 'success' : 
                               passedTests > totalTests / 2 ? 'warning' : 'error';
            
            summaryDiv.innerHTML = `
                <div class="test-item ${statusClass}">
                    <span class="status-indicator status-${passedTests === totalTests ? 'success' : 'warning'}"></span>
                    <strong>Test Summary:</strong> ${summary.join(' | ')}
                </div>
                <div class="test-item">
                    <strong>Status:</strong> ${passedTests === totalTests ? 
                        '✅ All systems operational!' : 
                        '⚠️ Some issues detected - check system status above'}
                </div>
            `;
        }
        
        // Run all tests
        async function runTests() {
            try {
                // Test API connection
                await testApiConnection();
                updateSystemStatus();
                
                // Load location script
                await loadLocationScript();
                updateSystemStatus();
                
                // Initialize location search
                initLocationSearch();
                updateSystemStatus();
                updateTestSummary();
                
            } catch (error) {
                console.error('Test failed:', error);
                updateSystemStatus();
                updateTestSummary();
            }
        }
        
        // Start tests when page loads
        window.onload = function() {
            runTests();
        };
    </script>
</body>
</html>
