<?php

/*
 * This file is part of Prokerala Astrology API PHP SDK
 *
 * © Ennexa Technologies <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Prokerala\Api\Astrology\Result\Horoscope\Papasamyam;

final class PapaPlanet
{
    private string $name;

    /**
     * @var PlanetDoshaDetails[]
     */
    private array $planetDosha;

    /**
     * PapaPlanet constructor.
     *
     * @param PlanetDoshaDetails[] $planetDosha
     */
    public function __construct(string $name, array $planetDosha)
    {
        $this->name = $name;
        $this->planetDosha = $planetDosha;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return PlanetDoshaDetails[]
     */
    public function getPlanetDosha(): array
    {
        return $this->planetDosha;
    }
}
