<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class PersonalYear implements ResultInterface
{
    use RawResponseTrait;

    private PersonalYearNumber $personalYearNumber;

    public function __construct(PersonalYearNumber $personalYearNumber)
    {
        $this->personalYearNumber = $personalYearNumber;
    }

    public function getPersonalYearNumber(): PersonalYearNumber
    {
        return $this->personalYearNumber;
    }
}
