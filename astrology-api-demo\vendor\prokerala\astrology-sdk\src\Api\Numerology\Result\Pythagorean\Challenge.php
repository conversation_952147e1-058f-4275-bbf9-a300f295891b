<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Challenge implements ResultInterface
{
    use RawResponseTrait;

    private ChallengeNumber $challengeNumber;

    public function __construct(ChallengeNumber $challengeNumber)
    {
        $this->challengeNumber = $challengeNumber;
    }

    public function getChallengeNumber(): ChallengeNumber
    {
        return $this->challengeNumber;
    }
}
