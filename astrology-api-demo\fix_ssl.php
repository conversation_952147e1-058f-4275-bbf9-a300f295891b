<?php

$phpIniPath = 'C:\php\php.ini';
$cacertPath = 'C:\php\cacert.pem';

echo "Fixing SSL certificate configuration...\n";

// Read the current php.ini content
$content = file_get_contents($phpIniPath);

// Configure cURL CA info
$content = str_replace(';curl.cainfo =', 'curl.cainfo = "' . $cacertPath . '"', $content);

// Also configure OpenSSL CA file for general SSL operations
$content = str_replace(';openssl.cafile=', 'openssl.cafile="' . $cacertPath . '"', $content);

// Write back to php.ini
file_put_contents($phpIniPath, $content);

echo "SSL configuration updated!\n";
echo "CA bundle downloaded to: $cacertPath\n";
echo "Please restart your web server or PHP process.\n";

?>
