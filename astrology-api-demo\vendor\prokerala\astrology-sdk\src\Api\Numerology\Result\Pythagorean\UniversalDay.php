<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class UniversalDay implements ResultInterface
{
    use RawResponseTrait;

    private UniversalDayNumber $universalDayNumber;

    public function __construct(UniversalDayNumber $universalDayNumber)
    {
        $this->universalDayNumber = $universalDayNumber;
    }

    public function getUniversalDayNumber(): UniversalDayNumber
    {
        return $this->universalDayNumber;
    }
}
