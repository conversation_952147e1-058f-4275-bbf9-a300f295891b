parameters:
	ignoreErrors:
		-
			message: "#^Method GuzzleHttp\\\\Client\\:\\:request\\(\\) should return Psr\\\\Http\\\\Message\\\\ResponseInterface but returns mixed\\.$#"
			count: 1
			path: src/Client.php

		-
			message: "#^Method GuzzleHttp\\\\Client\\:\\:send\\(\\) should return Psr\\\\Http\\\\Message\\\\ResponseInterface but returns mixed\\.$#"
			count: 1
			path: src/Client.php

		-
			message: "#^Method GuzzleHttp\\\\Client\\:\\:sendRequest\\(\\) should return Psr\\\\Http\\\\Message\\\\ResponseInterface but returns mixed\\.$#"
			count: 1
			path: src/Client.php

		-
			message: "#^Parameter \\#1 \\$str of function strtolower expects string, int\\|string given\\.$#"
			count: 1
			path: src/Client.php

		-
			message: "#^Parameter \\#1 \\$string of function strlen expects string, mixed given\\.$#"
			count: 1
			path: src/Cookie/SessionCookieJar.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 3
			path: src/Cookie/SetCookie.php

		-
			message: "#^Strict comparison using \\!\\=\\= between null and null will always evaluate to false\\.$#"
			count: 3
			path: src/Cookie/SetCookie.php

		-
			message: "#^Unsafe call to private method GuzzleHttp\\\\Exception\\\\RequestException\\:\\:obfuscateUri\\(\\) through static\\:\\:\\.$#"
			count: 1
			path: src/Exception/RequestException.php

		-
			message: "#^Cannot access offset 'version' on array\\|false\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Cannot call method getBody\\(\\) on Psr\\\\Http\\\\Message\\\\ResponseInterface\\|null\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_close expects resource, CurlHandle\\|resource given\\.$#"
			count: 2
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_error expects resource, CurlHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_getinfo expects resource, CurlHandle\\|resource given\\.$#"
			count: 4
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_reset expects resource, CurlHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_setopt expects resource, CurlHandle\\|resource given\\.$#"
			count: 4
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_setopt_array expects resource, CurlHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$str1 of function strcasecmp expects string, int\\|string given\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Property GuzzleHttp\\\\Handler\\\\CurlFactory\\:\\:\\$handles has unknown class CurlHandle as its type\\.$#"
			count: 1
			path: src/Handler/CurlFactory.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_errno expects resource, CurlHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlHandler.php

		-
			message: "#^Parameter \\#1 \\$ch of function curl_exec expects resource, CurlHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlHandler.php

		-
			message: "#^Method GuzzleHttp\\\\Handler\\\\CurlMultiHandler\\:\\:__get\\(\\) has invalid return type CurlMultiHandle\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Parameter \\#1 \\$mh of function curl_multi_add_handle expects resource, CurlMultiHandle\\|resource given\\.$#"
			count: 2
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Parameter \\#1 \\$mh of function curl_multi_close expects resource, CurlMultiHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Parameter \\#1 \\$mh of function curl_multi_exec expects resource, CurlMultiHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Parameter \\#1 \\$mh of function curl_multi_info_read expects resource, CurlMultiHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Parameter \\#1 \\$mh of function curl_multi_remove_handle expects resource, CurlMultiHandle\\|resource given\\.$#"
			count: 2
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Parameter \\#1 \\$mh of function curl_multi_select expects resource, CurlMultiHandle\\|resource given\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Property GuzzleHttp\\\\Handler\\\\CurlMultiHandler\\:\\:\\$_mh \\(CurlMultiHandle\\|resource\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Property GuzzleHttp\\\\Handler\\\\CurlMultiHandler\\:\\:\\$_mh has unknown class CurlMultiHandle as its type\\.$#"
			count: 1
			path: src/Handler/CurlMultiHandler.php

		-
			message: "#^Property GuzzleHttp\\\\Handler\\\\EasyHandle\\:\\:\\$handle has unknown class CurlHandle as its type\\.$#"
			count: 1
			path: src/Handler/EasyHandle.php

		-
			message: "#^Trying to invoke mixed but it's not a callable\\.$#"
			count: 1
			path: src/Handler/StreamHandler.php

		-
			message: "#^Variable \\$http_response_header on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/Handler/StreamHandler.php

		-
			message: "#^Variable \\$options in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: src/Handler/StreamHandler.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: src/HandlerStack.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 2
			path: src/Middleware.php

		-
			message: "#^Method GuzzleHttp\\\\Utils\\:\\:jsonDecode\\(\\) should return array\\|bool\\|float\\|int\\|object\\|string\\|null but returns mixed\\.$#"
			count: 1
			path: src/Utils.php

		-
			message: "#^Parameter \\#3 \\$depth of function json_decode expects int\\<1, max\\>, int given\\.$#"
			count: 1
			path: src/Utils.php

		-
			message: "#^Parameter \\#3 \\$depth of function json_encode expects int\\<1, max\\>, int given\\.$#"
			count: 1
			path: src/Utils.php

