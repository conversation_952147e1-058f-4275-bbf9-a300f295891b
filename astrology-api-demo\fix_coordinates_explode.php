<?php

echo "=== Fixing Coordinates Explode Issues ===\n\n";

// Find all PHP files in public directory
$publicDir = __DIR__ . '/public';
$files = glob($publicDir . '/*.php');

$filesWithIssue = [];
$filesFixed = [];

foreach ($files as $file) {
    $content = file_get_contents($file);
    $filename = basename($file);
    
    // Look for the problematic pattern: explode(',', $coordinates) without null check
    if (preg_match('/\$coordinates\s*=\s*\$_POST\[\'coordinates\'\];\s*.*?explode\(\',\',\s*\$coordinates\)/s', $content)) {
        $filesWithIssue[] = $filename;
        
        // Fix the issue by adding null check
        $newContent = preg_replace(
            '/(\$coordinates\s*=\s*\$_POST\[\'coordinates\'\];)\s*(.*?)(explode\(\',\',\s*\$coordinates\);)/s',
            '$1' . "\n" . '    if (!empty($coordinates)) {' . "\n" . '        $arCoordinates = $3' . "\n" . '        $input[\'latitude\'] = $arCoordinates[0] ?? \'\';' . "\n" . '        $input[\'longitude\'] = $arCoordinates[1] ?? \'\';' . "\n" . '    }',
            $content
        );
        
        // More specific pattern matching
        $patterns = [
            // Pattern 1: Basic coordinates explode
            '/(\$coordinates\s*=\s*\$_POST\[\'coordinates\'\];)\s*(\$arCoordinates\s*=\s*explode\(\',\',\s*\$coordinates\);)/' => 
            '$1' . "\n" . '    if (!empty($coordinates)) {' . "\n" . '        $2' . "\n" . '        $input[\'latitude\'] = $arCoordinates[0] ?? \'\';' . "\n" . '        $input[\'longitude\'] = $arCoordinates[1] ?? \'\';' . "\n" . '    }',
            
            // Pattern 2: With null coalescing
            '/(\$coordinates\s*=\s*\$_POST\[\'coordinates\'\]\s*\?\?\s*\'\';)/' => '$1',
        ];
        
        // Apply more targeted fix
        $lines = explode("\n", $content);
        $newLines = [];
        $inSubmitBlock = false;
        $coordinatesLineFound = false;
        $explodeLineFound = false;
        
        for ($i = 0; $i < count($lines); $i++) {
            $line = $lines[$i];
            
            // Check if we're in a submit block
            if (strpos($line, 'if (isset($_POST[\'submit\']))') !== false) {
                $inSubmitBlock = true;
            }
            
            if ($inSubmitBlock) {
                // Look for coordinates assignment without null coalescing
                if (preg_match('/\$coordinates\s*=\s*\$_POST\[\'coordinates\'\];/', $line)) {
                    $coordinatesLineFound = true;
                    $newLines[] = str_replace('$_POST[\'coordinates\'];', '$_POST[\'coordinates\'] ?? \'\';', $line);
                    continue;
                }
                
                // Look for explode line
                if ($coordinatesLineFound && preg_match('/\$arCoordinates\s*=\s*explode\(\',\',\s*\$coordinates\);/', $line)) {
                    $explodeLineFound = true;
                    $indent = str_repeat(' ', strlen($line) - strlen(ltrim($line)));
                    $newLines[] = $indent . 'if (!empty($coordinates)) {';
                    $newLines[] = $indent . '    ' . trim($line);
                    
                    // Add the latitude/longitude assignments if they're on the next lines
                    $j = $i + 1;
                    while ($j < count($lines) && (
                        strpos($lines[$j], '$input[\'latitude\']') !== false ||
                        strpos($lines[$j], '$input[\'longitude\']') !== false
                    )) {
                        $newLines[] = $indent . '    ' . trim($lines[$j]);
                        $i = $j; // Skip these lines in main loop
                        $j++;
                    }
                    
                    $newLines[] = $indent . '}';
                    continue;
                }
            }
            
            $newLines[] = $line;
        }
        
        if ($coordinatesLineFound || $explodeLineFound) {
            file_put_contents($file, implode("\n", $newLines));
            $filesFixed[] = $filename;
            echo "✅ Fixed: $filename\n";
        }
    }
}

echo "\nFiles with coordinates explode issue: " . count($filesWithIssue) . "\n";
foreach ($filesWithIssue as $file) {
    echo "  - $file\n";
}

echo "\nFiles fixed: " . count($filesFixed) . "\n";
foreach ($filesFixed as $file) {
    echo "  ✅ $file\n";
}

echo "\n=== Fix Complete ===\n";

?>
