<?php



namespace <PERSON>kerala\Api\Numerology\Result\Pythagorean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class InnerDream implements ResultInterface
{
    use RawResponseTrait;

    private InnerDreamNumber $innerDreamNumber;

    private NameChart $nameChart;

    public function __construct(InnerDreamNumber $innerDreamNumber, NameChart $nameChart)
    {
        $this->innerDreamNumber = $innerDreamNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getInnerDreamNumber(): InnerDreamNumber
    {
        return $this->innerDreamNumber;
    }
}
