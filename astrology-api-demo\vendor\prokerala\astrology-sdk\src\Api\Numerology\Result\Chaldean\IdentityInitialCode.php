<?php



namespace <PERSON>kerala\Api\Numerology\Result\Chaldean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;
use <PERSON>kerala\Api\Numerology\Result\Pythagorean\NameChart;

class IdentityInitialCode implements ResultInterface
{
    use RawResponseTrait;

    private IdentityInitialCodeNumber $identityInitialCodeNumber;

    private NameChart $nameChart;

    public function __construct(IdentityInitialCodeNumber $identityInitialCodeNumber, NameChart $nameChart)
    {
        $this->identityInitialCodeNumber = $identityInitialCodeNumber;
        $this->nameChart = $nameChart;
    }

    public function getNameChart(): NameChart
    {
        return $this->nameChart;
    }

    public function getIdentityInitialCodeNumber(): IdentityInitialCodeNumber
    {
        return $this->identityInitialCodeNumber;
    }
}
