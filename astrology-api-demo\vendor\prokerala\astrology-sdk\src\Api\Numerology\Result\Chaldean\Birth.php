<?php



namespace <PERSON>kerala\Api\Numerology\Result\Chaldean;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use Prokerala\Api\Astrology\Traits\Result\RawResponseTrait;

class Birth implements ResultInterface
{
    use RawResponseTrait;

    private BirthNumber $birthNumber;

    public function __construct(BirthNumber $birthNumber)
    {
        $this->birthNumber = $birthNumber;
    }

    public function getBirthNumber(): BirthNumber
    {
        return $this->birthNumber;
    }
}
