<?php

echo "=== Fixing Location Search CLIENT_ID Issues ===\n\n";

// Find all template files that use location search
$templateDir = __DIR__ . '/templates';
$files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($templateDir));

$templatesWithLocationSearch = [];
$templatesWithClientIdDefined = [];
$templatesNeedingFix = [];

foreach ($files as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $content = file_get_contents($file->getPathname());
        
        // Check if file uses location search
        if (strpos($content, 'prokerala-location-input') !== false || 
            strpos($content, 'LocationSearch') !== false) {
            
            $relativePath = str_replace(__DIR__ . '/', '', $file->getPathname());
            $templatesWithLocationSearch[] = $relativePath;
            
            // Check if CLIENT_ID is defined in JavaScript
            if (preg_match('/const\s+CLIENT_ID\s*=/', $content) || 
                preg_match('/CLIENT_ID\s*=\s*[\'"]/', $content)) {
                $templatesWithClientIdDefined[] = $relativePath;
            } else {
                $templatesNeedingFix[] = $relativePath;
            }
        }
    }
}

echo "Templates with location search: " . count($templatesWithLocationSearch) . "\n";
foreach ($templatesWithLocationSearch as $template) {
    echo "  - $template\n";
}

echo "\nTemplates with CLIENT_ID defined: " . count($templatesWithClientIdDefined) . "\n";
foreach ($templatesWithClientIdDefined as $template) {
    echo "  ✅ $template\n";
}

echo "\nTemplates needing CLIENT_ID fix: " . count($templatesNeedingFix) . "\n";
foreach ($templatesNeedingFix as $template) {
    echo "  ❌ $template\n";
}

// Fix the templates that need CLIENT_ID
echo "\n=== Applying Fixes ===\n";

foreach ($templatesNeedingFix as $template) {
    $filePath = __DIR__ . '/' . $template;
    $content = file_get_contents($filePath);
    
    // Look for the location search script start
    $pattern = '/<!-- CODE FOR LOCATION SEARCH STARTS -->\s*<script>\s*\(function \(\) \{/';
    
    if (preg_match($pattern, $content)) {
        $newContent = preg_replace(
            $pattern,
            "<!-- CODE FOR LOCATION SEARCH STARTS -->\n<script>\nconst CLIENT_ID = '<?= CLIENT_ID ?>';\n(function () {",
            $content
        );
        
        if ($newContent !== $content) {
            file_put_contents($filePath, $newContent);
            echo "✅ Fixed: $template\n";
        } else {
            echo "❌ Failed to fix: $template\n";
        }
    } else {
        echo "⚠️  Pattern not found in: $template\n";
    }
}

echo "\n=== Fix Complete ===\n";
echo "All templates should now have CLIENT_ID properly defined for location search!\n";

?>
