<?php



namespace <PERSON>kerala\Api\Horoscope\Result;

use <PERSON>kerala\Api\Astrology\Result\ResultInterface;
use <PERSON>kerala\Api\Astrology\Traits\Result\RawResponseTrait;

class DailyHoroscope implements ResultInterface
{
    use RawResponseTrait;

    private DailyHoroscopePrediction $dailyPrediction;

    public function __construct(DailyHoroscopePrediction $dailyPrediction)
    {
        $this->dailyPrediction = $dailyPrediction;
    }

    public function getDailyHoroscopePrediction(): DailyHoroscopePrediction
    {
        return $this->dailyPrediction;
    }
}
